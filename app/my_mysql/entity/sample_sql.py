########################################################
# 这个文件是一个sql表处理的示例文件，有一个sql表就要有一个对应的类似脚本写在这里。
# 需要先在rds使用sql语句创建表，然后使用这个脚本进行处理。
# 注意主键primary key的设置，必须是唯一的，而且应该是你用来搜索文档的主力。
########################################################











import json
from json.decoder import JSONDecodeError
from typing import List

from my_mysql import sql_client
from sqlalchemy import Table, Column, Integer, String, MetaData, insert, update, select, \
     text, TIMESTAMP, Index, and_, Text, or_, delete
from sqlalchemy.dialects.mysql import BIGINT, TINYINT
from sqlalchemy.exc import SQLAlchemyError
from utils.logger import logger

"""
CREATE TABLE `ai_batch_response` (
  `BatchJobID` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '父任务ID',
  `SubTaskID` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '子任务ID',
  `api_name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用的llm接口名',
  `query` varchar(1024) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户输入',
  `ai_response` text COLLATE utf8mb4_general_ci COMMENT 'llm返回内容',
  `ai_rating` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '问答评价',
  `ai_score` int NOT NULL COMMENT '问答打分（0-100）',
  PRIMARY KEY (`BatchJobID`,`SubTaskID`),
  KEY `idx_api_name` (`api_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='批量AI问答结果表'
"""

# 定义表
ai_batch_response = Table(
    'ai_batch_response', MetaData(),
    Column('BatchJobID', String(128), primary_key=True, comment='父任务ID'),
    Column('SubTaskID', String(128), primary_key=True, comment='子任务ID'),
    Column('api_name', String(64), nullable=False, comment='调用的llm接口名'),
    Column('query', String(1024), nullable=False, comment='用户输入'),
    Column('ai_response', Text, comment='llm返回内容'),
    Column('ai_rating', String(255), comment='问答评价'),
    Column('ai_score', Integer, nullable=False, comment='问答打分（0-100）'),
    Column('user_comment', String(255), nullable=True, comment='用户评论'),
    Column('user_score', Integer, nullable=True, comment='用户评分（0-100）'),
    Index('idx_api_name', 'api_name')
)

def insert_ai_batch_response(
    BatchJobID: str,
    SubTaskID: str,
    api_name: str,
    query: str,
    ai_response: str,
    ai_rating: str,
    ai_score: int
):
    ins = ai_batch_response.insert().values(
        BatchJobID=BatchJobID,
        SubTaskID=SubTaskID,
        api_name=api_name,
        query=query,
        ai_response=ai_response,
        ai_rating=ai_rating,
        ai_score=ai_score
    )
    try:
        sql_client.insert_return_id(ins)
        return True
    except Exception as e:
        logger.error(f"插入ai_batch_response失败: {e}")
        return False

def query_by_batch_or_subtask(batch_job_id=None, sub_task_id=None, api_name=None):
    # 支持 batch_job_id 或 sub_task_id 查询，可以都传，也可以只传一个
    conds = []
    if batch_job_id:
        conds.append(ai_batch_response.c.BatchJobID == batch_job_id)
    if sub_task_id:
        conds.append(ai_batch_response.c.SubTaskID == sub_task_id)
    if not conds:
        raise ValueError("必须至少指定 batch_job_id 或 sub_task_id")
    if api_name:
        conds.append(ai_batch_response.c.api_name == api_name)
    stmt = select(ai_batch_response).where(and_(*conds))
    try:
        return sql_client.select_many(stmt)
    except Exception as e:
        logger.error(f"查询ai_batch_response失败: {e}")
        return []

def query_by_batch_or_subtask_multi(batch_job_ids=None, sub_task_ids=None, api_name=None):
    # 支持批量 batch_job_ids 或 sub_task_ids 查询
    conds = []
    if batch_job_ids:
        conds.append(ai_batch_response.c.BatchJobID.in_(batch_job_ids))
    if sub_task_ids:
        conds.append(ai_batch_response.c.SubTaskID.in_(sub_task_ids))
    if not conds:
        raise ValueError("必须至少指定 batch_job_ids 或 sub_task_ids")
    if api_name:
        conds.append(ai_batch_response.c.api_name == api_name)
    stmt = select(ai_batch_response).where(and_(*conds))
    try:
        return sql_client.select_many(stmt)
    except Exception as e:
        logger.error(f"批量查询ai_batch_response失败: {e}")
        return []

def delete_by_ids(BatchJobID, SubTaskID, api_name):
    stmt = ai_batch_response.delete().where(
        and_(
            ai_batch_response.c.BatchJobID == BatchJobID,
            ai_batch_response.c.SubTaskID == SubTaskID,
            ai_batch_response.c.api_name == api_name
        )
    )
    try:
        return sql_client.update(stmt)
    except Exception as e:
        logger.error(f"删除ai_batch_response失败: {e}")
        return None

def delete_by_batch_job_id(BatchJobID):
    stmt = ai_batch_response.delete().where(
        ai_batch_response.c.BatchJobID == BatchJobID
    )
    try:
        return sql_client.update(stmt)
    except Exception as e:  
        logger.error(f"删除ai_batch_response失败: {e}")
        return None

def update_user_feedback(batch_job_id, sub_task_id, user_comment=None, user_score=None):
    """
    更新指定批次任务的用户反馈信息
    
    Args:
        batch_job_id (str): 父任务ID
        sub_task_id (str): 子任务ID
        user_comment (str, optional): 用户评论
        user_score (int, optional): 用户评分(0-100)
        
    Returns:
        bool: 更新成功返回True，失败返回False
    """
    if user_comment is None and user_score is None:
        logger.warning("没有提供需要更新的字段")
        return False
        
    # 构建更新数据
    update_values = {}
    if user_comment is not None:
        update_values["user_comment"] = user_comment
    if user_score is not None:
        # 确保评分在0-100范围内
        if not (0 <= user_score <= 100):
            logger.error(f"用户评分超出范围(0-100): {user_score}")
            return False
        update_values["user_score"] = user_score
    
    # 构建更新语句
    stmt = (
        ai_batch_response.update()
        .where(
            and_(
                ai_batch_response.c.BatchJobID == batch_job_id,
                ai_batch_response.c.SubTaskID == sub_task_id
            )
        )
        .values(**update_values)
    )
    
    try:
        result = sql_client.update(stmt)
        affected_rows = result.rowcount if result else 0
        if affected_rows > 0:
            logger.info(f"成功更新用户反馈 - BatchJobID: {batch_job_id}, SubTaskID: {sub_task_id}")
            return True
        else:
            logger.warning(f"未找到要更新的记录 - BatchJobID: {batch_job_id}, SubTaskID: {sub_task_id}")
            return False
    except Exception as e:
        logger.error(f"更新用户反馈失败: {e}")
        return False

# 测试函数 
def test_ai_batch_response():
    print("==== 测试插入 ====")
    insert_ai_batch_response(
        BatchJobID="batch1",
        SubTaskID="sub1",
        api_name="gpt-4",
        query="你好，AI！",
        ai_response="你好，有什么可以帮您？",
        ai_rating="优",
        ai_score=95
    )
    insert_ai_batch_response(
        BatchJobID="batch1",
        SubTaskID="sub2",
        api_name="gpt-4",
        query="你好，AI！",
        ai_response="你好，有什么可以帮您？",
        ai_rating="优",
        ai_score=95
    )
    insert_ai_batch_response(
        BatchJobID="batch2",
        SubTaskID="sub1",
        api_name="gpt-4",
        query="你好，AI！",
        ai_response="你好，有什么可以帮您？",
        ai_rating="优",
        ai_score=95
    )
    insert_ai_batch_response(
        BatchJobID="batch2",
        SubTaskID="sub2",
        api_name="gpt-4",
        query="你好，AI！",
        ai_response="你好，有什么可以帮您？",
        ai_rating="优",
        ai_score=95
    )
    print("==== 测试按BatchJobID查 ====")
    res1 = query_by_batch_or_subtask(batch_job_id="batch1")
    print(res1)
    print("==== 测试按SubTaskID查 ====")
    res2 = query_by_batch_or_subtask(sub_task_id="sub1")
    print(res2)
    print("==== 测试批量查 ====")
    res3 = query_by_batch_or_subtask_multi(batch_job_ids=["batch1", "batch2"], sub_task_ids=["sub1", "sub2"])
    print(res3)
    print("==== 测试删除 ====")
    delete_by_ids("batch1", "sub1", "gpt-4")
    delete_by_ids("batch2", "sub2", "gpt-4")
    delete_by_ids("batch1", "sub2", "gpt-4")
    delete_by_ids("batch2", "sub1", "gpt-4")
    res4 = query_by_batch_or_subtask(batch_job_id="batch1")
    print(res4)

if __name__ == '__main__':
    update_user_feedback("liulingfeng05_batch_test_005", "liulingfeng05_batch_test_005_agentic_0", user_comment="答得不错我喜欢", user_score=90)


