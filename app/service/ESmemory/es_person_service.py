from utils.logger import logger
from service.ESmemory.es_memory_client import client
from uuid import uuid4
from datetime import datetime

def add_person(index_name:str,person_id:str="",is_user:bool=False,canonical_name:str="",aliases:str="",relationships:list=[],profile_summary:str="",key_attributes:dict={},avatar:str=""): #添加人员
    if not person_id:person_id=str(uuid4())
    try:
        doc={"person_id":person_id,"is_user":is_user,"canonical_name":canonical_name,"aliases":aliases,"relationships":relationships,"profile_summary":profile_summary,"key_attributes":key_attributes,"avatar":avatar}
        response=client.index(index=index_name,id=person_id,body=doc)
        logger.info(f"添加人员成功: {person_id}")
        return {"result":"success","person_id":person_id,"response":response}
    except Exception as e:
        logger.error(f"添加人员失败: {str(e)}")
        return {"result":"error","reason":str(e)}

def get_all_persons(index_name:str,size:int=100): #获取所有人员列表
    try:
        search_body={"size":size,"query":{"match_all":{}}}
        response=client.search(index=index_name,body=search_body)
        persons=[hit["_source"] for hit in response["hits"]["hits"]]
        return {"result":"success","persons":persons,"total":response["hits"]["total"]["value"]}
    except Exception as e:
        logger.error(f"获取人员列表失败: {str(e)}")
        return {"result":"error","reason":str(e),"persons":[]}

def get_person_by_id(index_name:str,person_id:str): #根据ID获取人员详情
    try:
        response=client.get(index=index_name,id=person_id)
        return {"result":"success","person":response["_source"]}
    except Exception as e:
        logger.error(f"获取人员详情失败: {str(e)}")
        return {"result":"error","reason":str(e),"person":None}

def update_person(index_name:str,person_id:str,**kwargs): #更新人员信息
    try:
        doc={"doc":{k:v for k,v in kwargs.items() if v is not None}}
        response=client.update(index=index_name,id=person_id,body=doc)
        logger.info(f"更新人员成功: {person_id}")
        return {"result":"success","response":response}
    except Exception as e:
        logger.error(f"更新人员失败: {str(e)}")
        return {"result":"error","reason":str(e)}

def search_persons_by_name(index_name:str,name:str,size:int=10): #按姓名搜索人员
    try:
        search_body={"size":size,"query":{"multi_match":{"query":name,"fields":["canonical_name","aliases"],"analyzer":"ik_smart"}}}
        response=client.search(index=index_name,body=search_body)
        persons=[{"person":hit["_source"],"score":hit["_score"]} for hit in response["hits"]["hits"]]
        return {"result":"success","persons":persons}
    except Exception as e:
        logger.error(f"搜索人员失败: {str(e)}")
        return {"result":"error","reason":str(e),"persons":[]} 