from datetime import datetime
from service.ESmemory.es_memory_client import client
from utils.logger import logger
from configs.lion_config import get_value
import json
from service.ai_client import send_to_ai
from service.ESmemory.es_memory_upsert import upsert_memory
from vex.vex_thrift import embedding
from typing import List, Dict, Any
def update_memory(index_name:str, memory_content:str, doc_id:str):
    """
    更新已有的记忆
    """
    try:
        body = {
            "doc": {
                "memory_content": memory_content,
                "memory_vector": embedding(memory_content)
            },
            "doc_as_upsert": True   
        }
        response = client.update(index=index_name, id=doc_id, body=body)
        return response
    except Exception as e:
        logger.error(f"更新记忆失败: {str(e)}")
        return None
    

def has_time_elapsed(latest_date: str, time_range: float) -> bool:
    """
    检查当前时间是否超过latest_date超过time_range天
    
    Args:
        latest_date: 一个字符串，格式为'YYYY-MM-DD-HH-MM'
        time_range: 时间阈值，单位为天（float）
        
    Returns:
        bool: 如果当前时间超过latest_date超过time_range天，返回True，否则返回False
    """
    # 将latest_date字符串转换为datetime对象
    latest_datetime = datetime.strptime(latest_date, "%Y-%m-%d-%H-%M")
    
    # 获取当前时间
    current_datetime = datetime.now()
    
    # 计算时间差，单位为秒
    time_difference = current_datetime - latest_datetime
    
    # 将时间差转换为天
    days_difference = time_difference.total_seconds() / (24 * 60 * 60)
    
    # 如果时间差超过time_range天，返回True，否则返回False
    return days_difference > time_range


def generate_search_body_by_id(user_id, memory_type):
    """
    生成搜索体，给定user id对应最多一千条文档，全部是短期记忆
    Args:
        user_id: 用户id
    """
    try:
        
        return {
            "size": 1000,
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"user_id": user_id}},
                        {"term": {"memory_type": memory_type}}
                    ]
                }
            }
        }
    except Exception as e:
        logger.error(f"生成搜索体失败: {str(e)}")
        # 返回一个简单的空查询
        return {
            "size": 0,
            "query": {
                "match_none": {}
            }
        }
    
def search_memory_by_id(index_name:str, user_id:str, memory_type:str):
    """
    给定user id搜索最多一千条对应的文档
    """
        
    try:
        # 生成搜索体
        search_body = generate_search_body_by_id(user_id, memory_type)
        
        # 执行搜索操作
        response = client.search(index=index_name, body=search_body)
        return response
    except Exception as e:
        logger.error(f"搜索{index_name}出错: {str(e)}")
        # 返回空的搜索结果，避免程序崩溃
        return {"hits": {"hits": [], "total": {"value": 0}}}
    
def search_clean_memory_by_id(index_name:str, user_id:str, memory_type:str):
    """
    给定user id搜索最多一千条对应的文档，返回纯净的记忆（不会显示向量属性）。
    """
    try:
        response = search_memory_by_id(index_name, user_id, memory_type)
        response_data = response['hits']['hits']
        response_data = [{
            "id": hit['_id'],
            "memory_content": hit['_source']['memory_content'],
            "memory_type": hit['_source']['memory_type'],
            "topic": hit['_source']['topic'],
            "time_stamp": hit['_source']['time_stamp'],
            "user_id": hit['_source']['user_id']
        } for hit in response_data]
        return response_data
    except Exception as e:
        logger.error(f"搜索{index_name}出错: {str(e)}")
        # 返回空的搜索结果，避免程序崩溃
        return []
        

def generate_update_search_body(topic, size, k, user_id, memory_type):
    """
    生成搜索体
        Args:
        topic: 记忆主题
        size: 返回结果数量
        k: 候选数量
        user_id: 用户id
        memory_type: 记忆类型
    """
    try:
        return {
            "size": size,
            "_source": {
                "excludes": ["topic_vector"]
            },
            "query": {
                "knn": {
                    "field": "topic_vector",
                    "query_vector": embedding(topic),
                    "num_candidates": k,
                    "filter": [
                    {"term": {"user_id": user_id}},
                    {"term": {"memory_type": memory_type}}
                    ]
                }
            }
        }
    except Exception as e:
        logger.error(f"生成搜索体失败: {str(e)}")
        # 返回一个简单的空查询，避免程序崩溃
        return {
            "size": 0,
            "query": {
                "match_none": {}
            }
        }

def search_update_memory(index_name:str, topic:str, size:int, k:int, user_id:str, memory_type:str):
    """
    搜索文档中的长期记忆以便整合更新，一个示例搜索体格式：
    search_body = {
        "size": 5,
        "_source": {
            "excludes": ["topic_vector"]
        },
        "query": {
            "knn": {
                "field": "topic_vector",
                "query_vector": embedding("烤鸡"),
                "num_candidates": k,
                "filter": [
                    {"term": {"user_id": user_id}},
                    {"term": {"memory_type": memory_type}}
                ]
            }
        }
    }
    """
    try:
        response = client.search(index=index_name, body=generate_update_search_body(topic, size, k, user_id, memory_type))

        response = response['hits']['hits']
        response = [{
            "id": hit['_id'],
            "topic": hit['_source']['topic'],
            "memory_content": hit['_source']['memory_content'],
            "time_stamp": hit['_source']['time_stamp'],
            "user_id": hit['_source']['user_id']
        } for hit in response]
        return response
    except Exception as e:
        logger.error(f"搜索{index_name}出错: {str(e)}")
        # 返回一个空的搜索结果，避免程序崩溃
        return {"hits": {"hits": [], "total": {"value": 0}}}


def delete_memory(index_name:str, doc_id:str):
    """
    给定doc id删除单个文档
    """
    if not doc_id:
        logger.warning("文档ID为空，跳过删除")
        return {"result": "skipped", "reason": "empty_id"}
        
    try:
        response = client.delete(index=index_name, id=doc_id)
        logger.info(f"删除{index_name}文档成功: {doc_id}")
        return response
    except Exception as e:
        logger.error(f"删除{index_name}出错: {str(e)}")
        # 返回错误信息，避免程序崩溃
        return {"result": "error", "reason": str(e)}


def delete_memories_by_user_id(index_name:str, user_id:str):
    """
    删除指定用户ID的所有文档，测试用
    
    Args:
        index_name: 索引名称
        user_id: 用户ID
        
    Returns:
        dict: 包含删除结果的统计信息
    """
    if not user_id:
        logger.warning("用户ID为空，跳过批量删除")
        return {"result": "skipped", "reason": "empty_user_id"}
        
    try:
        # 构建查询，查找指定user_id的所有文档
        query = {
            "query": {
                "term": {
                    "user_id": user_id
                }
            }
        }
        
        # 先尝试统计匹配文档数量
        try:
            count_response = client.count(index=index_name, body=query)
            total_docs = count_response.get('count', 0)
        except Exception as e:
            logger.error(f"统计用户{user_id}的文档数量失败: {str(e)}")
            total_docs = 0
        
        # 如果没有匹配的文档，直接返回
        if total_docs == 0:
            logger.info(f"用户{user_id}没有需要删除的文档")
            return {"total": 0, "deleted": 0, "failed": 0}
        
        # 使用delete_by_query批量删除
        try:
            response = client.delete_by_query(index=index_name, body=query)
            deleted = response.get('deleted', 0)
            logger.info(f"从{index_name}删除用户{user_id}的文档: 共{deleted}/{total_docs}条")
            
            return {
                "total": total_docs,
                "deleted": deleted,
                "failed": total_docs - deleted if total_docs > deleted else 0
            }
        except Exception as e:
            logger.error(f"批量删除用户{user_id}的文档失败: {str(e)}")
            return {"total": total_docs, "deleted": 0, "failed": total_docs}
        
    except Exception as e:
        logger.error(f"删除用户{user_id}的文档出错: {str(e)}")
        return {
            "error": str(e),
            "total": 0,
            "deleted": 0
        }
    
def delete_outdated_memories(index_name:str, user_id:str):
    """
    删除过期文档，时间范围在lion获取
    """
    all_memories = search_memory_by_id(index_name, user_id, "short")
    time_range = float(get_value("humanrelation.update_time_range", 15))
    for memory in all_memories['hits']['hits']:
        if has_time_elapsed(memory['_source']['time_stamp'], time_range):
            delete_memory(index_name, memory['_id'])

def summarize_short_memories(short_memories):
    """
    使用AI将短期记忆总结为长期记忆
    
    Args:
        short_memories: 短期记忆列表
    
    Returns:
        dict or list: 生成的长期记忆(可能是单条记忆或多条记忆列表)
    """
    if not short_memories or len(short_memories) == 0:
        logger.warning("没有短期记忆可供总结")
        return None
    
    try:
        # 从配置获取提示词，默认使用local_config中的定义
        prompt = get_value(
            "humanrelation.long_memory_summarization_prompt", 
        )
        
        # 准备发送给AI的数据
        messages = [
            {
                "role": "system",
                "content": prompt
            },
            {
                "role": "user",
                "content": f"请根据以下短期记忆集合，总结出用户的长期记忆：\n\n{json.dumps(short_memories, ensure_ascii=False, indent=2)}"
            }
        ]
        
        # 调用AI服务
        data = {
            "model": get_value("humanrelation.memory_model", "gpt-4o-mini"),
            "messages": messages,
            "temperature": 0.5,
            "max_tokens": 2000,
        }
        
        response = send_to_ai(data)
        response_text = response.text
        logger.info(f"总结短期记忆response: {response_text}")
        # 解析AI响应
        try:
            response_json = json.loads(response_text)
            if "choices" in response_json and len(response_json["choices"]) > 0:
                ai_response = response_json["choices"][0]["message"]["content"]
                # 尝试解析AI返回的JSON
                long_memory = json.loads(ai_response)
                
                logger.info(f"生成长期记忆成功: {long_memory}")
                return long_memory
            else:
                logger.error(f"AI响应格式错误: {response_json}")
                return None
        except json.JSONDecodeError as e:
            logger.error(f"解析AI响应JSON失败: {e}, 响应内容: {response_text}")
            return None
            
    except Exception as e:
        logger.error(f"总结短期记忆时发生错误: {e}")
        return None

def integrate_memories(new_memory, search_results, user_id):
    """
    判断新生成的长期记忆是否需要与搜索到的现有长期记忆整合
    
    Args:
        new_memory: 新生成的长期记忆
        search_results: 搜索到的相关长期记忆
        user_id: 用户ID
        
    Returns:
        list: 整合决策结果，包含memory_content和doc_id
    """
    if not search_results or len(search_results) == 0:
        # 如果没有搜索结果，直接返回新记忆，doc_id为空
        logger.info(f"没有搜索结果，直接返回新记忆，doc_id为空")
        return [{
            "memory_content": new_memory,
            "doc_id": ""
        }]
    
    try:
        # 从配置获取提示词
        prompt = get_value(
            "humanrelation.memory_integration_prompt", 
        )
        
        # 准备发送给AI的数据
        messages = [
            {
                "role": "system",
                "content": prompt
            },
            {
                "role": "user",
                "content": f"""
请判断以下新生成的长期记忆是否需要与现有长期记忆整合：

新记忆：
{json.dumps(new_memory, ensure_ascii=False, indent=2)}

现有记忆：
{json.dumps(search_results, ensure_ascii=False, indent=2)}
"""
            }
        ]
        
        # 调用AI服务
        data = {
            "model": get_value("humanrelation.memory_model", "gpt-4o-mini"),
            "messages": messages,
            "temperature": 0.3,
            "max_tokens": 1000,
        }
        
        response = send_to_ai(data)
        response_text = response.text
        logger.info(f"记忆整合判断response: {response_text}")
        
        # 解析AI响应
        try:
            response_json = json.loads(response_text)
            if "choices" in response_json and len(response_json["choices"]) > 0:
                ai_response = response_json["choices"][0]["message"]["content"]
                # 尝试解析AI返回的JSON
                integration_results = json.loads(ai_response)
                
                logger.info(f"记忆整合判断结果: {integration_results}")
                return integration_results
            else:
                logger.error(f"AI响应格式错误: {response_json}")
                return [{
                    "memory_content": new_memory,
                    "doc_id": ""
                }]
        except json.JSONDecodeError as e:
            logger.error(f"解析AI响应JSON失败: {e}, 响应内容: {response_text}")
            return [{
                "memory_content": new_memory,
                "doc_id": ""
            }]
            
    except Exception as e:
        logger.error(f"记忆整合判断时发生错误: {e}")
        return [{
            "memory_content": new_memory,
            "doc_id": ""
        }]
    
def update_last_modified_time(index_name:str, user_id:str):
    """
    更新用户更新时间
    """
    try:
        update_body = {
            "doc": {
                "latest_date": datetime.now().strftime("%Y-%m-%d-%H-%M"),
                "mis_id": user_id
            },
            "doc_as_upsert": True
        }
        response = client.update(index=index_name, id=user_id, body=update_body)
        logger.info(f"更新用户更新时间成功: {response}，更新体: {update_body}")
        return response
    except Exception as e:
        logger.error(f"更新用户更新时间时发生错误: {e}")
        return {"result": "error", "reason": str(e)}

def update_long_memory(index_name:str, user_id:str):
    """
    更新长期记忆 - 将用户的短期记忆总结为长期记忆并存储
    
    Args:
        index_name: 索引名称
        user_id: 用户ID
    
    Returns:
        bool: 是否成功更新长期记忆
    """
    try:
        # 获取用户的短期记忆
        all_short_memories = search_memory_by_id(index_name, user_id, "short")
        
        # 检查是否有短期记忆
        if all_short_memories["hits"]["total"]["value"] == 0:
            logger.info(f"用户 {user_id} 没有短期记忆可供更新")
            return False
            
        # 提取短期记忆
        short_memories = []
        for memory in all_short_memories['hits']['hits']:
            short_memories.append({
                "memory_content": memory['_source']['memory_content'],
                "time_stamp": memory['_source']['time_stamp'],
                "topic": memory['_source']['topic']
            })
            
        # 总结短期记忆为长期记忆
        long_memory = summarize_short_memories(short_memories)
        
        if not long_memory:
            logger.warning(f"用户 {user_id} 的短期记忆无法总结为长期记忆")
            return False
        
        # 存储成功标志
        storage_success = False
            
        integrate_candidate_num = int(get_value("humanrelation.integrate_candidate_num", 10))
        # 处理生成的长期记忆
        for memory in long_memory:
            # 搜索相关的长期记忆
            search_result = search_update_memory(index_name, memory['topic'], integrate_candidate_num, integrate_candidate_num, user_id, "long")
            
            # 判断是否需要与现有记忆整合
            integration_results = integrate_memories(memory['memory_content'], search_result, user_id)
            
            # 根据整合结果处理记忆
            contain_new_memory = True
            for result in integration_results:
                if result["doc_id"]:
                    # 如果有doc_id，说明需要更新现有记忆
                    try:
                        contain_new_memory = False
                        update_response = update_memory(index_name, result["memory_content"], result["doc_id"])
                        if update_response.get("result") == "updated":
                            logger.info(f"成功为用户 {user_id} 更新整合长期记忆 {result['memory_content']}")
                            storage_success = True
                        else:
                            logger.error(f"用户 {user_id} 的整合长期记忆更新失败: {update_response}")
                    except Exception as e:
                        logger.error(f"更新整合长期记忆时出错: {e}")

            if contain_new_memory:
                try:
                    upsert_memory(
                        index_name=index_name, 
                        user_id=user_id, 
                        memory_content=result["memory_content"], 
                        memory_type="long", 
                        topic=memory['topic']
                    )
                    logger.info(f"成功为用户 {user_id} 存储新长期记忆 {result['memory_content']}")
                    storage_success = True
                except Exception as e:
                    logger.error(f"用户 {user_id} 的新长期记忆 {result['memory_content']} 存储失败")
                    logger.error(f"错误信息: {e}")
            
        # 更新用户更新时间
        if storage_success:
            update_date_index = get_value("humanrelation.update_date_index", "memory_last_modified_date")
            update_last_modified_time(update_date_index, user_id)
        return storage_success
            
    except Exception as e:
        logger.error(f"更新长期记忆时发生错误: {e}")
        return False

if __name__ == "__main__":

    index_name = "memory_es_hr_test"
    print(search_clean_memory_by_id(index_name, "liulingfeng05", "short"))
    print(search_clean_memory_by_id(index_name, "liulingfeng05", "long"))
 
    # search_result = search_update_memory(index_name, "烤鱼", 2, 2, "liulingfeng05", "long")
    # print(search_result)

    # delete_memories_by_user_id(index_name, "liulingfeng05")