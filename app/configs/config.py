import os

APP_KEY = "com.sankuai.friday.xiaomei.humanrelations"
API_MEITUAN_AI = 'https://aigc.sankuai.com/v1/openai/native/chat/completions'
CURRENT_ENV = "test"
CELL = ""
try:
    with open(file="/data/webapps/appenv") as f:
        for line in f:
            new_line_list = line.split("=")
            if len(new_line_list) >= 2:
                key = new_line_list[0].strip()
                value = new_line_list[1].strip().replace("\n", "").replace("\r", "").replace("\t", "")
                if key == "env":
                    CURRENT_ENV = value
                    print("CURRENT_ENV: " + CURRENT_ENV)
                elif key == "cell":
                    CELL = value
                    print("CELL: " + CELL)

except FileNotFoundError:
    # 如果找不到默认配置文件，尝试使用本地配置
    local_config = os.path.join(os.path.dirname(__file__), "appenv")
    if os.path.exists(local_config):
        with open(file=local_config) as f:
            for line in f:
                new_line_list = line.split("=")
                if len(new_line_list) >= 2 and new_line_list[0] == "env":
                    CURRENT_ENV = new_line_list[1].replace(" ", "").replace("\n", "").replace("\r", "").replace("\t", "")
                    break



if CURRENT_ENV == "test":
    # TODO: 其实adcode也不能写死，但是只是内部使用是不是先这样也可以
    API_WEATHER = "https://weather.ai.test.sankuai.com/weather/realtimeWeather?adcode=110105"
    API_WEATHER_HOURLY = "https://weather.ai.test.sankuai.com/weather/hourlyWeather"
    API_HOLIDAY = "http://timor.tech/api/holiday/info/"
    LION_ENV = "test"
    MERCHANT_DATA_URL = "http://10.171.59.195:8080/merchant/search"
elif CURRENT_ENV == 'prod':
    API_WEATHER = "http://10.243.161.93:8080/weather/realtimeWeather?adcode=110105"
    API_WEATHER_HOURLY = "http://10.243.161.93:8080/weather/hourlyWeather"
    API_HOLIDAY = "http://timor.tech/api/holiday/info/"
    LION_ENV = "prod"
    MERCHANT_DATA_URL = "http://10.108.184.48:8080/merchant/search"


ZEBRA_REF_KEY = "maas_xiaomei_weiwei_test" if CURRENT_ENV == "test" else "friday_xiaomei_weiwei_product"

INGREDIENT_ANALYSIS_RESULTS_DIR = "data/ingredient_analysis_results"
CHOSEN_MODEL = "False"

DEFAULT_MODEL_CONFIG = """{
    "model_name":"gpt-4.1",
    "temperature":0.0
}"""

DEFAULT_SYSTEM_PROMPT = """
你是一个乐于助人的人力资源助理。请总是使用中文进行友好和专业的回答。
"""