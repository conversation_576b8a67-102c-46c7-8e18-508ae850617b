import signal
import sys
import uvicorn
from app import app

def signal_handler(signum, frame):
    print(f"收到信号 {signum}，准备关闭服务器...")
    http_server.stop()
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)

print("---------------- wsgi start ----------\n\n")
http_server = uvicorn.run(app, host="0.0.0.0", port=8080)

try:
    http_server.serve_forever()
except Exception as e:
    print(f"服务器运行出错: {str(e)}")
    sys.exit(1)