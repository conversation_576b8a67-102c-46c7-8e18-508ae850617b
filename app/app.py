import fastapi
from fastapi.responses import StreamingResponse
import uvicorn
from pydantic import BaseModel
from configs.lion_config import get_value
from service.ESmemory.es_memory_callback import search_memory
from configs.config import APP_KEY
from pycat import Cat
from agents.hr import get_agent_response_stream
from uuid import uuid4
from service.ESmemory.es_memory_processor import process_and_store_memory,retrieve_relevant_memory
from service.mysql_person_service import get_all_persons_mysql,get_person_by_id_mysql,add_person,update_person_mysql,delete_person_mysql,search_persons_by_name_mysql #改为MySQL人员服务
from service.enhanced_memory_service import EnhancedMemoryService #增强记忆服务
from typing import List,Optional
import threading
import time
from datetime import datetime
from my_mysql.entity.reminders import query_reminders_by_status_and_time
from utils.logger import logger

Cat.init_cat(APP_KEY)
import json

Cat.init_cat(APP_KEY)

# 全局变量用于追踪后台线程
reminder_thread = None
thread_start_time = None

def reminder_background_task():
    """
    后台任务：每小时查询一次需要处理的提醒
    """
    logger.info("后台提醒任务线程开始运行")
    while True:
        try:
            # 查询状态为'active'且触发时间已到的提醒
            current_time = datetime.now() ###这里的时间需要弄好，决定好时间的格式，比如YYYY-MM-DD
            active_reminders = query_reminders_by_status_and_time('active', current_time)
            
            if active_reminders:
                logger.info(f"发现 {len(active_reminders)} 条需要处理的提醒")
                for reminder in active_reminders:
                    logger.info(f"提醒ID: {reminder['reminder_id']}, "
                              f"用户ID: {reminder['user_id']}, "
                              f"触发时间: {reminder['next_trigger_time']}")
                    # 这里可以添加具体的提醒处理逻辑
                    # 比如发送通知、更新状态等
            else:
                logger.info("当前没有需要处理的提醒")
                
        except Exception as e:
            logger.error(f"后台提醒任务执行失败: {e}")
        
        # 休眠1小时 (3600秒)
        time.sleep(3600)

def start_reminder_thread():
    """
    启动提醒后台线程
    """
    global reminder_thread, thread_start_time
    
    if reminder_thread and reminder_thread.is_alive():
        logger.warning("提醒线程已经在运行中")
        return False
    
    reminder_thread = threading.Thread(target=reminder_background_task, daemon=True)
    reminder_thread.start()
    thread_start_time = datetime.now()
    logger.info("后台提醒检查线程已启动")
    return True

def is_reminder_thread_alive():
    """
    检查提醒线程是否还活着
    """
    global reminder_thread
    return reminder_thread is not None and reminder_thread.is_alive()

app = fastapi.FastAPI()

index_name = get_value("humanrelation.memory_index_name")

# 初始化增强记忆服务
enhanced_memory = EnhancedMemoryService()

class SearchMemoryRequest(BaseModel):
    user_input: str #用户输入内容，根据语义相关性查询
    size: int #返回结果数量
    k: int #候选数量
    user_id: str #查询者的用户 mis id
    memory_type: str #记忆类型，分long or short

class ChatRequest(BaseModel):
    content: str  # 用户问题
    conversation_id: str  # 对话 ID，用于上下文关联
    user_id: str # 用户ID

class AddMemoryRequest(BaseModel):
    conversation_text: str #对话文本
    user_id: str #用户ID

@app.get("/")
def read_root():
    return "Hello World"

class ReadMemoryRequest(BaseModel):
    query_text: str #查询文本
    user_id: str #用户ID
    max_results: int = 10 #最大结果数

class PersonRequest(BaseModel):
    user_id: str #用户ID
    canonical_name: str #正式姓名
    aliases: str = "" #别名
    relationships: list = [] #人际关系
    profile_summary: str = "" #个人简介
    key_attributes: dict = {} #关键属性
    avatar: str = "" #头像URL
    is_user: bool = False #是否为用户

class MergePersonsRequest(BaseModel):
    user_id: str #用户ID
    primary_person_id: str
    secondary_person_id: str

@app.get("/humanrelation/reminder_thread_status")
def check_reminder_thread_status():
    """
    监察接口：检查后台提醒线程状态
    """
    is_alive = is_reminder_thread_alive()
    status_message = "线程正常运行" if is_alive else "线程已停止"
    start_time_str = thread_start_time.strftime("%Y-%m-%d %H:%M:%S") if thread_start_time else None
    
    result = {"is_alive": is_alive, "start_time": start_time_str, "status_message": status_message}
    return result

@app.post("/humanrelation/restart_reminder_thread")
def restart_reminder_thread():
    """
    重启后台提醒线程
    """
    try:
        success = start_reminder_thread()
        if success:
            return {"success": True, "message": "后台提醒线程重启成功"}
        else:
            return {"success": False, "message": "线程已在运行中，无需重启"}
    except Exception as e:
        logger.error(f"重启提醒线程失败: {e}")
        return {"success": False, "message": f"重启失败: {str(e)}"}

@app.post("/humanrelation/search_memory")
def search_memory_endpoint(request: SearchMemoryRequest):
    result = search_memory(
        index_name=index_name,
        user_input=request.user_input,
        size=request.size,
        k=request.k,
        user_id=request.user_id,
        memory_type=request.memory_type
    )
    return result

@app.post("/humanrelation/chat", summary="流式聊天接口")
def chat_endpoint(request: ChatRequest):
    """流式聊天接口，使用 conversation_id 维护上下文"""
    response_stream = get_agent_response_stream(request.content, request.conversation_id, request.user_id)
    return StreamingResponse(response_stream, media_type="text/event-stream")

@app.post("/humanrelation/chat_json")
def chat_json_endpoint(request: ChatRequest):
    """非流式聊天接口，返回JSON格式"""
    try:
        # 使用同步方式调用记忆服务
        from agents.hr import graph
        from langchain.schema import HumanMessage
        
        inputs = {
            "messages": [HumanMessage(content=request.content)],
            "user_id": request.user_id
        }
        config = {"configurable": {"thread_id": request.conversation_id}}
        
        # 同步调用图
        result = graph.invoke(inputs, config=config)
        
        # 提取AI回复
        if result and "messages" in result and result["messages"]:
            ai_message = result["messages"][-1]
            content = ai_message.content if hasattr(ai_message, 'content') else str(ai_message)
            return {"response": content, "status": "success"}
        else:
            return {"response": "抱歉，我暂时无法回答您的问题。", "status": "error"}
    except Exception as e:
        return {"response": f"处理请求时发生错误: {str(e)}", "status": "error"}

@app.post("/humanrelation/add_memory",summary="添加记忆")
def add_memory(request:AddMemoryRequest):
    """从对话文本中提取并存储人员和事件记忆"""
    return enhanced_memory.extract_and_process_memory(request.conversation_text,request.user_id)

@app.post("/humanrelation/read_memory",summary="读取记忆")  
def read_memory(request:ReadMemoryRequest):
    """根据查询文本检索相关的人员和事件记忆"""
    return enhanced_memory.retrieve_memory_for_conversation(request.query_text,request.user_id,request.max_results)

@app.get("/humanrelation/persons",summary="查看所有人列表")
def get_persons(user_id:str,size:int=100,offset:int=0):
    """获取所有人员列表"""
    return get_all_persons_mysql(user_id,size,offset)

@app.get("/humanrelation/person/{person_id}",summary="查看某个人")
def get_person(user_id:str,person_id:str):
    """根据ID获取人员详细信息"""
    return get_person_by_id_mysql(user_id,person_id)

@app.delete("/humanrelation/person/{person_id}",summary="删除某个人")
def delete_person(user_id:str,person_id:str):
    """删除指定人员"""
    return delete_person_mysql(user_id,person_id)

@app.post("/humanrelation/add_person",summary="增加某个人")
def create_person(request:PersonRequest):
    """添加新人员"""
    return add_person(user_id=request.user_id,canonical_name=request.canonical_name,aliases=request.aliases,relationships=request.relationships,profile_summary=request.profile_summary,key_attributes=request.key_attributes,avatar=request.avatar,is_user=request.is_user)

@app.put("/humanrelation/change_person/{person_id}",summary="修改某个人的档案")
def update_person(person_id:str,request:PersonRequest):
    """更新人员档案信息"""
    return update_person_mysql(user_id=request.user_id,person_id=person_id,canonical_name=request.canonical_name,aliases=request.aliases,relationships=request.relationships,profile_summary=request.profile_summary,key_attributes=request.key_attributes,avatar=request.avatar,is_user=request.is_user)

@app.get("/humanrelation/search_person",summary="搜索某个人")
def search_person(user_id:str,name:str,limit:int=10):
    """按姓名搜索人员"""
    return search_persons_by_name_mysql(user_id,name,limit)

@app.post("/humanrelation/update_long_memory",summary="更新长期记忆")
def update_long_memory(user_id:str):
    """手动触发长期记忆更新，将短期记忆总结为人物小传"""
    return enhanced_memory.update_long_term_memory(user_id)

@app.get("/humanrelation/check_outdated",summary="检查过期信息")
def check_outdated_info(user_id:str,days:int=180):
    """检查需要更新的过期状态信息"""
    return enhanced_memory.check_outdated_information(user_id,days)

@app.post("/humanrelation/merge_persons",summary="合并两个人员档案")
def merge_persons(request:MergePersonsRequest):
    """确认并执行两个人员档案的合并"""
    return enhanced_memory.execute_merge_persons(request.user_id,request.primary_person_id,request.secondary_person_id)

@app.get("/monitor/alive", summary="健康检查接口")
def health_check():
    """健康检查接口，用于部署时的存活性检查"""
    return {"status": "ok", "message": "Service is alive"}

@app.on_event("startup")
async def startup_event():
    """
    应用启动事件：启动后台提醒检查线程
    """
    start_reminder_thread()

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)