import json
from configs.lion_config import get_value
from configs.config import DEFAULT_MODEL_CONFIG, DEFAULT_SYSTEM_PROMPT
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, AIMessage, HumanMessage, ToolMessage
from langgraph.graph import StateGraph, END, START
from langgraph.checkpoint.memory import MemorySaver
from utils.logger import logger
from agents.state import State
from service.enhanced_memory_service import EnhancedMemoryService
from langgraph.prebuilt import ToolNode, tools_condition
from tools.base import get_all_tools, add_tools, clear_all_tools
from tools.example import calculator_plus

# 检查 OpenAI API 密钥
COMPANY_OPENAI_API_BASE = "https://aigc.sankuai.com/v1/openai/native"
COMPANY_OPENAI_API_KEY = str(get_value("xiaomei.humanrelation_openai_apikey", "1930188146638651430"))

# 初始化增强记忆服务
memory_service = EnhancedMemoryService()

# --- LangGraph 核心逻辑 ---

clear_all_tools()
core_tools = [
    calculator_plus
]
add_tools(core_tools)
tools_list = get_all_tools()
tool_node = ToolNode(tools_list)
logger.info(f"tools: {tools_list}")

def process_memory_and_context(state: State): #记忆处理和上下文增强节点
    """处理用户输入的记忆并为对话添加上下文"""
    messages = state['messages']
    if not messages: return state
    
    last_message = messages[-1]
    user_input = last_message.content if hasattr(last_message, 'content') else str(last_message)
    user_id = state.get('user_id', 'default_user') # 直接从state获取user_id
    
    try:
        # 1. 提取并自动存储记忆 (新逻辑)
        # 注意: 此函数现在内部直接写入DB
        memory_result = memory_service.extract_and_process_memory(user_input, user_id)
        
        # 2. 检索相关记忆为对话提供上下文
        retrieval_result = memory_service.retrieve_memory_for_conversation(user_input, user_id, 3)
        
        # 3. 构建增强的系统提示，现在需要传入新的memory_result结构
        enhanced_prompt = _build_enhanced_system_prompt(retrieval_result, memory_result)
        
        # 4. 更新消息列表
        enhanced_messages = [SystemMessage(content=enhanced_prompt)] + [m for m in messages if not isinstance(m, SystemMessage)]
        
        return {"messages": enhanced_messages, "memory_context": retrieval_result}
    except Exception as e:
        logger.error(f"记忆处理失败: {str(e)}")
        return state

def call_model(state: State): #调用模型节点
    """调用 OpenAI 模型节点"""
    messages = state['messages']
    
    # 打印最终发送给回答模型的内容
    logger.info("--- 向最终回答模型发送的内容 ---")
    for msg in messages:
        logger.info(f"角色: {msg.type}, 内容: {msg.content}")
    logger.info("-----------------------------")

    model_config = json.loads(str(get_value("xiaomei.humanrelation.model_config", DEFAULT_MODEL_CONFIG)))

    logger.info(f"model_config: {model_config},COMPANY_OPENAI_API_KEY: {COMPANY_OPENAI_API_KEY}")
    
    # 初始化 OpenAI 模型
    my_max_tokens = model_config.get("max_tokens", 1024)
    if "anthropic" in model_config["model_name"]:
        custom_headers = {"Authorization": f"Bearer {COMPANY_OPENAI_API_KEY}"}
        model = ChatOpenAI(model_name=model_config["model_name"], temperature=model_config["temperature"], streaming=True, openai_api_base=COMPANY_OPENAI_API_BASE, openai_api_key=COMPANY_OPENAI_API_KEY,default_headers=custom_headers,max_tokens=my_max_tokens)
    else:
        model = ChatOpenAI(
            model_name=model_config["model_name"],
            temperature=model_config["temperature"],
            streaming=True,
            openai_api_base=COMPANY_OPENAI_API_BASE,
            openai_api_key=COMPANY_OPENAI_API_KEY,
            max_tokens=my_max_tokens
        )

    # 【新增】将工具绑定到模型上，让模型知道有哪些工具可用
    model_with_tools = model.bind_tools(tools_list)

    # 为了避免每次都重复添加，检查第一条消息是否为 SystemMessage
    if not messages or not isinstance(messages[0], SystemMessage):
        # 插入系统消息，为 Agent 定义角色
        messages.insert(0, SystemMessage(content=DEFAULT_SYSTEM_PROMPT))

    # 调用模型
    response = model_with_tools.invoke(messages)
    
    return {"messages": [response]}

def _build_enhanced_system_prompt(retrieval_result: dict, memory_result: dict): #构建增强的系统提示
    """基于检索到的记忆构建增强的系统提示"""
    try:
        base_prompt = DEFAULT_SYSTEM_PROMPT
        
        if retrieval_result.get("result") != "success":
            return base_prompt
        
        persons = retrieval_result.get("persons", [])
        events = retrieval_result.get("events", [])
        suggestions = retrieval_result.get("suggestions", "")
        
        memory_context = ""
        
        # 添加人物信息（长期记忆）
        if persons:
            memory_context += "\n\n【相关人物档案】\n"
            for person in persons[:2]:  # 最多显示2个相关人物
                memory_context += f"• {person.get('canonical_name', '')}: {person.get('profile_summary', '')}\n"
                key_attrs = person.get('key_attributes', {})
                if key_attrs:
                    attrs_text = ", ".join([f"{k}: {v}" for k, v in key_attrs.items() if not k.endswith('_更新时间') and not k.startswith('verification')])
                    if attrs_text:
                        memory_context += f"  关键信息: {attrs_text}\n"
        
        # 添加相关事件（短期记忆）
        if events:
            memory_context += "\n【相关事件记忆】\n"
            for event in events[:3]:  # 最多显示3个相关事件
                memory_context += f"• {event.get('description_text', '')}\n"
                if event.get('location'):
                    memory_context += f"  地点: {event.get('location')}\n"
                if event.get('sentiment'):
                    memory_context += f"  情感: {event.get('sentiment')}\n"
        
        # 添加对话建议
        if suggestions:
            memory_context += f"\n【对话建议】\n{suggestions}\n"
        
        # 添加验证信息
        if memory_result.get("action_code") == "PROCESS_COMPLETE":
            persons_results = memory_result.get("payload",{}).get("persons",[])
            for person_res in persons_results:
                # 从新的结果结构中提取验证消息
                verification_msg = person_res.get("verification_message","")
                if verification_msg:
                    memory_context += f"\n【需要确认】\n{verification_msg}\n"
        
        if memory_context:
            enhanced_prompt = f"{base_prompt}\n\n=== 记忆上下文 ==={memory_context}\n请结合以上记忆信息进行对话，提供个性化和有针对性的回应。"
            return enhanced_prompt
        
        return base_prompt
    except Exception as e:
        logger.error(f"构建增强提示失败: {str(e)}")
        return DEFAULT_SYSTEM_PROMPT

# 1. 构建图
workflow = StateGraph(State)

# 2. 添加节点
workflow.add_node("memory_processor", process_memory_and_context)
workflow.add_node("agent", call_model)

workflow.add_node("tools", tool_node)

# 3. 定义图的流程
workflow.set_entry_point("memory_processor")
workflow.add_edge("memory_processor", "agent")
workflow.add_conditional_edges(
    "agent",
    tools_condition,
    {
        "tools": "tools",
        END: END
    }
)
workflow.add_edge("tools", "agent")

# 4. 设置记忆
memory = MemorySaver()

# 5. 编译图
graph = workflow.compile(checkpointer=memory)

# --- 流式响应生成器 ---

async def get_agent_response_stream(user_input: str, conversation_id: str, user_id: str): #流式响应生成器
    """异步生成器函数，供 FastAPI 调用"""
    # 定义 LangGraph 的标准输入格式，直接将user_id注入state
    inputs = {
        "messages": [HumanMessage(content=user_input)],
        "user_id": user_id
    }
    
    # 定义配置，仅包含thread_id
    config = {"configurable": {"thread_id": conversation_id}}
    
    # 累积收到的内容，实现"叠加"输出
    accumulated = ""
    # 使用 astream_events V2 接口
    async for event in graph.astream_events(inputs, config=config, version="v2"):
        kind = event["event"]

        logger.info(f"current event: {event}")  ##慎用，一大堆文字，会打印很多
        if kind == "on_chat_model_stream":
            content = event["data"]["chunk"].content
            if content:
                accumulated += content
                data = json.dumps({"content": accumulated, "type": "chat"}, ensure_ascii=False)
                yield f"data: {data}\n\n"

        elif kind == "on_tool_start":
            tool_name = event["name"]
            tool_input = event["data"]["input"]
            result = json.dumps({"tool_name": tool_name, "tool_input": tool_input, "status": "start"}, ensure_ascii=False)
            yield f"data: {result}\n\n"

        elif kind == "on_tool_end":
            tool_name = event["name"]
            tool_output = event["data"]["output"].content
            result = json.dumps({"tool_name": tool_name, "tool_output": tool_output, "status": "end"}, ensure_ascii=False)
            yield f"data: {result}\n\n"
